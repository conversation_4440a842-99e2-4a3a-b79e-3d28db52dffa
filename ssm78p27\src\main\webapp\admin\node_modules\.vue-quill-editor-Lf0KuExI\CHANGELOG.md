
## CHANGELOG

### v3.0.6

PR194, Prevents auto-focus when using within components with dynamically set content.

### v3.0.5

update umd module name and rebuild

### v3.0.4

1. fix object assign in spa

### v3.0.3

1. fix import es module bug
2. add test script

### v3.0.2

1. assign options to ssr.js

### v3.0.1

1. add lib object-assign
2. update the options assign logic

### v3.0.0

#### use
1. require styles [#111](https://github.com/surmon-china/vue-quill-editor/issues/111)
2. add global default options [#110](https://github.com/surmon-china/vue-quill-editor/issues/110)
3. update `{ editor, text, html }` to `{ quill, text, html }`

#### project
- add brower support
- add test scripts
- update babel and webpack configs
