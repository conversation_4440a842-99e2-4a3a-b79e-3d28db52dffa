<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>组件 | vue-amap</title>
  <meta name="description" content="Description">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">

  <link rel="stylesheet" href="//unpkg.com/docsify/lib/themes/vue.css">
  <link rel="stylesheet" href="//unpkg.com/vuep/dist/vuep.css">

  <script src="//unpkg.com/vue@2.5.3/dist/vue.min.js"></script>
  <script src="//unpkg.com/vuep/dist/vuep.min.js"></script>
</head>
<body>
  <div id="app"></div>
</body>
<script>
  window.$docsify = {
    loadSidebar: true,
    coverpage: true,
    auto2top: true,
    alias: {
      '/zh-cn/.*/_sidebar.md': '/_sidebar.md'
    }
  }
</script>

<style>
  .vuep {
    flex-direction: column;
    clear: both;
    overflow: hidden;
    box-shadow: 0 0 13px #a0a0a0;
    height: auto;
    margin: 30px 0;
  }

  .vuep-editor {
    height: 300px;
  }

  .vuep-editor,
  .vuep-preview {
    width: auto;
    overflow: visible;
    margin-right: 0;
  }

  .vuep-preview {
    padding: 0;
  }

  .amap-demo {
    height: 300px;
  }

  .toolbar {
    padding: 10px;
  }

  .toolbar button{
    background: #42b983;
    border: 0;
    color: white;
    padding: 8px;
    margin: 0 5px;
    border-radius: 3px;
    cursor: pointer;
  }

  .search-box {
    position: absolute;
    top: 15px;
    left: 20px;
  }

  .amap-page-container {
    position: relative;
  }
</style>

<script type="text/javascript">
  window.VueAMap.initAMapApiLoader({
    key: '160cab8ad6c50752175d76e61ef92c50',
    plugin: ['Autocomplete', 'PlaceSearch', 'Scale', 'OverView', 'ToolBar', 'MapType', 'PolyEditor', 'AMap.CircleEditor', 'Geolocation', 'Geocoder', 'MarkerClusterer', 'ElasticMarker'],
    uiVersion: '1.0',
    v: '1.4.6'
  });
</script>

<script src="//unpkg.com/docsify/lib/docsify.min.js"></script>
<script>
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

ga('create', 'UA-101628170-1', 'auto');
ga('send', 'pageview');
</script>
</html>
