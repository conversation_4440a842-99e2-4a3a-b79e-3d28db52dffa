{"name": "vue-amap", "version": "0.5.10", "description": "vue amap", "main": "./dist/index.js", "sideEffects": false, "directories": {"doc": "docs"}, "scripts": {"dev": "node build/dev-server.js --content-base dist/", "build": "node build/build.js", "lib": "npm run test && export LIB_ENV=lib && node build/lib.js", "start": "node build/dev-server.js", "lint": "eslint src", "test": "npm run lint && npm run test:unit", "prepublishOnly": "npm run lib", "test:unit": "karma  start test/unit/karma.conf.js --single-run", "test:dev": "karma  start test/unit/karma.conf.js --single-run=false"}, "repository": {"type": "git", "url": "git+https://github.com/ElemeFE/vue-amap.git"}, "files": ["dist", "src"], "author": "elemefe", "license": "MIT", "bugs": {"url": "https://github.com/ElemeFE/vue-amap/issues"}, "homepage": "https://github.com/ElemeFE/vue-amap#readme", "devDependencies": {"autoprefixer": "^6.4.0", "babel-core": "^6.26.0", "babel-eslint": "^7.1.1", "babel-loader": "^7.1.2", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-runtime": "^6.0.0", "babel-preset-env": "^1.6.1", "babel-runtime": "^6.26.0", "chai": "^3.5.0", "connect-history-api-fallback": "^1.4.0", "copy-webpack-plugin": "^4.2.0", "cross-env": "^3.1.3", "cross-spawn": "^4.0.2", "css-loader": "^0.28.7", "eslint": "^3.7.1", "eslint-config-elemefe": "^0.1.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.5.0", "eslint-plugin-html": "^1.3.0", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^1.0.0", "eventsource-polyfill": "^0.9.6", "express": "^4.16.2", "extract-text-webpack-plugin": "^2.0.0", "fast-deep-equal": "^1.0.0", "file-loader": "^0.9.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.1", "html-webpack-plugin": "^2.30.1", "http-proxy-middleware": "^0.17.2", "husky": "^0.14.3", "inject-loader": "^2.0.1", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-sinon-chai": "^1.3.2", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.5", "less": "^2.7.3", "less-loader": "^4.0.5", "lolex": "^1.5.2", "mocha": "^3.5.3", "normalize.css": "^5.0.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "particles.js": "^2.0.0", "script-loader": "^0.7.2", "selenium-server": "2.53.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.14.0", "url-loader": "^0.5.8", "vue": "^2.5.13", "vue-loader": "^14.1.1", "vue-markdown-loader": "^0.6.0", "vue-style-loader": "^2.0.0", "vue-template-compiler": "^2.5.13", "webpack": "^3.10.0", "webpack-bundle-analyzer": "^2.2.1", "webpack-dashboard": "^0.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.20.0", "webpack-merge": "^2.6.1"}, "dependencies": {"uppercamelcase": "^1.1.0"}, "engines": {"core-js": "^2.5.0", "node": ">= 4.0.0", "npm": ">= 3.0.0"}}