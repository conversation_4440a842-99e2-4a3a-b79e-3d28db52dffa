{"name": "camelcase", "version": "1.2.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": "sindresorhus/camelcase", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}}