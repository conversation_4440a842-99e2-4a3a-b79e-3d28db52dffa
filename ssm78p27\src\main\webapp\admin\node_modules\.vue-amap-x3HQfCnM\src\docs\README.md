# vue-amap
[![Build Status](https://travis-ci.org/ElemeFE/vue-amap.svg?branch=master)](https://travis-ci.org/ElemeFE/vue-amap)
[![npm package](https://img.shields.io/npm/v/vue-amap.svg)](https://www.npmjs.org/package/vue-amap)
[![NPM downloads](http://img.shields.io/npm/dm/vue-amap.svg)](https://npmjs.org/package/vue-amap)
![JS gzip size](http://img.badgesize.io/https://unpkg.com/vue-amap/src/lib/index.js?compression=gzip&label=gzip%20size:%20JS)
[![license](https://img.shields.io/github/license/elemefe/vue-amap.svg?style=flat-square)](https://github.com/ElemeFE/vue-amap)
[![GitHub stars](https://img.shields.io/github/stars/elemefe/vue-amap.svg?style=social&label=Star)](https://github.com/ElemeFE/vue-amap)

> vue-amap是一套基于Vue 2.0和高德地图的地图组件。

## npm

推荐 npm 安装。

```
npm install vue-amap --save
```

## CDN

目前可通过 [unpkg.com/vue-amap](https://unpkg.com/vue-amap/dist/index.js) 获取最新版本的资源。

```html
<script src="https://unpkg.com/vue-amap/dist/index.js"></script>
```
