{"name": "vue-router", "version": "3.6.5", "description": "Official router for Vue.js 2", "author": "<PERSON>", "license": "MIT", "main": "dist/vue-router.common.js", "module": "dist/vue-router.esm.js", "unpkg": "dist/vue-router.js", "jsdelivr": "dist/vue-router.js", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/vue-router.git"}, "typings": "types/index.d.ts", "files": ["src", "dist/*.js", "dist/*.mjs", "types/*.d.ts", "composables.mjs", "composables.js", "composables.d.ts", "vetur/tags.json", "vetur/attributes.json"], "exports": {".": {"import": {"node": "./dist/vue-router.mjs", "default": "./dist/vue-router.esm.js"}, "require": "./dist/vue-router.common.js", "types": "./types/index.d.ts"}, "./composables": {"import": "./composables.mjs", "require": "./composables.js", "types": "./composables.d.ts"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package.json": "./package.json"}, "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "keywords": ["vue", "router", "routing"], "scripts": {"dev": "node examples/server.js", "dev:dist": "rollup -wm -c build/rollup.dev.config.js", "build": "node build/build.js", "lint": "eslint src examples test", "test": "npm run lint && npm run flow && npm run test:unit && npm run test:e2e && npm run test:types", "flow": "flow check", "test:unit": "jasmine JASMINE_CONFIG_PATH=test/unit/jasmine.json", "test:e2e": "node test/e2e/runner.js", "test:e2e:ci": "node test/e2e/runner.js --local -e ie,android44 -c test/e2e/nightwatch.browserstack.js test/e2e/specs/active-links.js", "test:e2e:ff": "node test/e2e/runner.js -e firefox -c test/e2e/nightwatch.config.js", "test:e2e:ie9": "node test/e2e/runner.js --local -e ie9 -c test/e2e/nightwatch.browserstack.js --skiptags history,ie9-fail", "test:types": "tsc -p types/test", "docs": "vuepress dev docs", "docs:build": "vuepress build docs", "changelog": "conventional-changelog -p angular -r 2 -i CHANGELOG.md -s", "release": "bash scripts/release.sh", "commit": "git-cz"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommitMsg.js"}, "lint-staged": {"*.{js,vue}": ["eslint --fix", "git add"]}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@vuepress/plugin-pwa": "^1.5.3", "@vuepress/theme-vue": "^1.5.3", "axios": "^0.21.1", "babel-core": "^6.24.1", "babel-eslint": "^10.0.2", "babel-loader": "^7.1.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-preset-env": "^1.6.1", "babel-preset-flow-vue": "^1.0.0", "browserstack-local": "^1.4.8", "buble": "^0.19.8", "chromedriver": "^96.0.0", "conventional-changelog-cli": "^2.0.11", "cross-spawn": "^7.0.3", "css-loader": "^2.1.1", "dotenv": "^8.2.0", "es6-promise": "^4.2.8", "eslint": "^4.19.1", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-jasmine": "^2.10.1", "eslint-plugin-vue-libs": "^2.1.0", "express": "^4.17.1", "express-urlrewrite": "^1.2.0", "flow-bin": "^0.66.0", "geckodriver": "^1.20.0", "jasmine": "2.8.0", "lint-staged": "^8.2.0", "nightwatch": "^1.3.6", "nightwatch-helpers": "^1.0.0", "path-to-regexp": "^1.8.0", "rollup": "^2.34.1", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-flow-no-whitespace": "^1.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-watch": "^4.0.0", "selenium-server": "^3.141.59", "terser": "^4.2.0", "typescript": "^4.7.0", "vue": "^2.7.0", "vue-loader": "^15.9.3", "vue-server-renderer": "^2.7.0", "vue-template-compiler": "^2.7.0", "vuepress": "^1.5.3", "vuepress-theme-vue": "^1.1.1", "webpack": "^4.35.2", "webpack-dev-middleware": "^3.7.0", "yorkie": "^2.0.0"}, "bugs": {"url": "https://github.com/vuejs/vue-router/issues"}, "homepage": "https://github.com/vuejs/vue-router#readme"}