{"name": "vue-json-excel", "version": "0.3.0", "description": "Download your JSON as an excel or CSV file directly from the browser", "main": "dist/vue-json-excel.umd.js", "module": "dist/vue-json-excel.esm.js", "scripts": {"build:dist": "rollup -c ./rollup.config.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/jecovier/vue-json-excel.git"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "vue2", "Excel", "xls", "csv", "json", "export", "json excel", "download", "component"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jecovier/vue-json-excel/issues"}, "homepage": "https://github.com/jecovier/vue-json-excel#readme", "dependencies": {"downloadjs": "^1.4.7"}, "devDependencies": {"rollup": "^1.7.4", "rollup-plugin-commonjs": "^9.2.2", "rollup-plugin-node-resolve": "^4.0.1", "rollup-plugin-vue": "^4.7.2", "vue-template-compiler": "^2.6.10"}}